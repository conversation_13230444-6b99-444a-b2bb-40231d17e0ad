`节点`
文件、类、方法、注解、注释
`边`
contains（包含）
depends_on（依赖）
instance_of（实例化）
documented_by（文档）
calls（调用）
out_calls(外部调用)
implemented_by(方法实现)
overridden_by(方法重写)
`关系`
类 依赖 类：实现、继承
类 包含 类：引用（成员变量），内部类
类 包含 方法：类包含哪些方法
类 包含 注解实例：类使用了一个注解实例
方法 依赖 类：方法参数使用到了类
方法 调用 方法：方法使用到了另一个方法
方法 外部调用 方法：方法调用了项目外部的一个方法
方法 实现于 方法：方法被另一个方法实现
方法 重写于 方法：方法被另一个方法重写
方法 包含 注解实例：方法使用了一个注解实例
注解实例 实例化 类：注解实例属于注解类的实例
方法 文档 注释：方法的注释
类 文档 注释：类的注释

## 节点设计
```sql
CREATE TAG IF NOT EXISTS file(

  file_path string NOT NULL COMMENT '文件路径',

  name string NOT NULL COMMENT '文件名',

  extension string COMMENT '文件扩展名',
       
  is_library bool DEFAULT false COMMENT '是否为库文件',

  last_modified timestamp COMMENT '最后修改时间',

  language string COMMENT '编程语言',

  branch_name string COMMENT '所属分支名称',

  commit_status string DEFAULT 'UNCOMMITTED' COMMENT '提交状态: COMMITTED, UNCOMMITTED',

  commit_id string COMMENT '若已提交，对应的提交ID',

  last_updated timestamp COMMENT '最后更新时间',

  repo_id string COMMENT '所属仓库ID'

) COMMENT = '文件节点';
```


  ```sql
CREATE TAG IF NOT EXISTS class(

  name string NOT NULL COMMENT '类名',

  full_name string NOT NULL COMMENT '包含命名空间的完整类名',

  type string DEFAULT 'class' COMMENT '类型: class, interface, enum, annotation',

  visibility string DEFAULT 'public' COMMENT '可见性（public/private/protected）',
       
  is_library bool DEFAULT false COMMENT '是否为库类',

  line_start int COMMENT '开始行号',

  line_end int COMMENT '结束行号',

  branch_name string COMMENT '所属分支名称',

  commit_status string DEFAULT 'UNCOMMITTED' COMMENT '提交状态: COMMITTED, UNCOMMITTED',

  commit_id string COMMENT '若已提交，对应的提交ID',

  last_updated timestamp COMMENT '最后更新时间',

  repo_id string COMMENT '所属仓库ID',

  is_external bool DEFAULT false COMMENT '是否为外部文件',

  content string COMMENT '内容'

) COMMENT = 'class节点';
```


```sql
CREATE TAG IF NOT EXISTS function(

  name string NOT NULL COMMENT '函数名',

  full_name string NOT NULL COMMENT '包含命名空间的完整函数名',

  visibility string DEFAULT 'public' COMMENT '可见性',

  is_static bool DEFAULT false COMMENT '是否为静态方法',

  is_constructor bool DEFAULT false COMMENT '是否为构造函数',
       
  is_library bool DEFAULT false COMMENT '是否为库函数',

  line_start int COMMENT '开始行号',

  line_end int COMMENT '结束行号',

  complexity int DEFAULT 1 COMMENT '圈复杂度',

  branch_name string COMMENT '所属分支名称',

  commit_status string DEFAULT 'UNCOMMITTED' COMMENT '提交状态: COMMITTED, UNCOMMITTED',

  commit_id string COMMENT '若已提交，对应的提交ID',

  last_updated timestamp COMMENT '最后更新时间',

  repo_id string COMMENT '所属仓库ID',

  content string COMMENT '内容'

) COMMENT = '函数/方法节点';
```



```sql
CREATE TAG IF NOT EXISTS comment(

  type string DEFAULT 'LINE' COMMENT '注释类型（LINE-行注释, BLOCK-块注释, DOC-文档注释',

  line_start int COMMENT '开始行号',

  line_end int COMMENT '结束行号',

  branch_name string COMMENT '所属分支名称',

  commit_status string DEFAULT 'UNCOMMITTED' COMMENT '提交状态: COMMITTED, UNCOMMITTED',

  commit_id string COMMENT '若已提交，对应的提交ID',

  last_updated timestamp COMMENT '最后更新时间',

  repo_id string COMMENT '所属仓库ID',

  content string COMMENT '内容'

) COMMENT = '注释节点';
```


```sql
CREATE TAG IF NOT EXISTS annotations(

  name string NOT NULL COMMENT '注解名',

  full_name string NOT NULL COMMENT '包含命名空间的完整注解名',

  type string DEFAULT 'ANNOTATION' COMMENT '注解类型, ANNOTATION, MARKER_ANNOTATION',

  line_start int COMMENT '开始行号',

  line_end int COMMENT '结束行号',

  branch_name string COMMENT '所属分支名称',

  commit_status string DEFAULT 'UNCOMMITTED' COMMENT '提交状态: COMMITTED, UNCOMMITTED',

  commit_id string COMMENT '若已提交，对应的提交ID',

  last_updated timestamp COMMENT '最后更新时间',

  repo_id string COMMENT '所属仓库ID',

  content string COMMENT '内容'

) COMMENT = '注解节点';

```


## 边设计

```sql
CREATE EDGE IF NOT EXISTS contains(
) COMMENT = '包含关系';
```

```sql
CREATE EDGE IF NOT EXISTS depends_on(

  dependency_type string COMMENT '依赖类型（IMPORT, USAGE, CALL, CREATION, ASSOCIATION等）',
  line_number int COMMENT '代码行号'

) COMMENT = '依赖关系';
```

```sql
CREATE EDGE IF NOT EXISTS instance_of(

  line_number int COMMENT '代码行号'

) COMMENT = '实例化关系';
```

```sql
CREATE EDGE IF NOT EXISTS documented_by(

) COMMENT = '注释关系';
```

```sql
CREATE EDGE IF NOT EXISTS calls(

  line_number int COMMENT '代码行号'

) COMMENT = '调用关系';
```

```sql
CREATE EDGE IF NOT EXISTS out_calls(

  line_number int COMMENT '代码行号'

) COMMENT = '外部方法调用关系';
```

```sql
CREATE EDGE IF NOT EXISTS  implemented_by(
) COMMENT = '方法实现关系';
```

```sql
CREATE EDGE IF NOT EXISTS  overridden_by(
) COMMENT = '方法重写关系';
```

```sql
CREATE TAG INDEX idx_class_full_name ON class(full_name(256));
REBUILD TAG INDEX idx_class_full_name;
CREATE TAG INDEX idx_function_name ON function(name(256));
REBUILD TAG INDEX idx_function_name;
CREATE TAG INDEX IF NOT EXISTS file_repo_branch on file(repo_id(256), branch_name(256));
REBUILD TAG INDEX file_repo_branch;
CREATE TAG INDEX IF NOT EXISTS class_repo_branch on class(repo_id(256), branch_name(256));
REBUILD TAG INDEX class_repo_branch;
CREATE TAG INDEX IF NOT EXISTS function_repo_branch on function(repo_id(256), branch_name(256));
REBUILD TAG INDEX function_repo_branch;
CREATE TAG INDEX IF NOT EXISTS comment_repo_branch on comment(repo_id(256), branch_name(256));
REBUILD TAG INDEX comment_repo_branch;
CREATE TAG INDEX IF NOT EXISTS annotations_repo_branch on annotations(repo_id(256), branch_name(256));
REBUILD TAG INDEX annotations_repo_branch;
```