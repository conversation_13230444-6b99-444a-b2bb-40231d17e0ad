# Python后端
__pycache__/
*.py[cod]
*$py.class
*.so
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
backend/logs/
*.log

# 前端
node_modules/
/frontend/build
/frontend/dist
/frontend/.env*
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*
/frontend/.pnp
/frontend/.pnp.js
/frontend/coverage
.npm

# 数据库
*.db
*.sqlite3

# IDE和编辑器
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db
.project
.classpath
.settings/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
.tmp/
.temp/
tmp/
temp/
*.bak
*.tmp

# 配置文件
config.local.js
local_settings.py
*.local.yml

# 其他
.history/
.pytest_cache/
.hypothesis/
.coverage
htmlcov/ 

/backend/app/config.properties
