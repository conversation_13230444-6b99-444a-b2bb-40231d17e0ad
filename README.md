# Maling Query - 知识图谱查询服务

基于FastMCP的知识图谱查询服务，通过Milvus向量数据库和NebulaGraph图数据库提供代码知识图谱查询功能。

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Milvus 2.3+
- NebulaGraph 3.4+

### 2. 安装和配置

```bash
# 克隆项目
git clone <repository-url>
cd maling-query/backend

# 设置开发环境（推荐）
python setup_dev.py

# 配置服务
cp app/config.properties.template app/config.properties
# 编辑配置文件，填入Milvus和NebulaGraph连接信息
```

### 3. 启动服务

```bash
# 进入backend目录
cd backend

# HTTP模式（默认，提供REST API和MCP服务）
python run.py

# STDIO模式（仅MCP服务，用于进程间通信）
python run.py stdio

# 运行测试
python run.py test
```

## 📁 项目结构

```
maling-query/
├── backend/                 # 后端服务
│   ├── app/                # 应用代码
│   │   ├── api/           # API接口
│   │   ├── base/          # 基础组件
│   │   ├── service/       # 业务服务
│   │   ├── test/          # 测试工具
│   │   └── utils/         # 工具类
│   ├── main.py            # 主入口文件
│   └── requirements.txt   # Python依赖
├── pyproject.toml         # 项目配置
├── setup_dev.py          # 开发环境设置
├── run.py                # 统一运行脚本
└── README.md             # 项目说明
```

## 🔧 开发指南

### 导入规范

项目采用统一的导入规范：

- **应用代码**：使用相对导入 `from app.xxx import xxx`
- **测试脚本**：使用动态路径设置，然后相对导入

### 开发环境设置

```bash
# 进入backend目录
cd backend

# 安装为开发模式（推荐）
python setup_dev.py

# 或手动安装
pip install -e .
```

### 运行测试

```bash
# 进入backend目录
cd backend

# 使用统一脚本
python run.py test

# 或直接运行
python test_hybrid_search.py
```

## 🛠️ 功能特性

### MCP工具

- `start_session`: 开启对话会话
- `query_knowledge_graph`: 查询知识图谱（支持混合检索）
- `get_node_content`: 获取节点详细内容
- `close_session`: 关闭会话

### 混合检索

支持稠密向量 + 稀疏文本的混合检索，提供更准确的搜索结果：

- **稠密向量搜索**：基于语义相似度
- **稀疏文本搜索**：基于关键词匹配
- **RRF融合**：使用Reciprocal Rank Fusion算法合并结果

## 📊 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │    │   FastAPI App    │    │  Knowledge Graph│
│                 │◄──►│                  │◄──►│    Service      │
│ (Claude/Cursor) │    │  REST + MCP APIs │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                              ┌──────────────────────────┼──────────────────────────┐
                              │                          │                          │
                              ▼                          ▼                          ▼
                    ┌─────────────────┐        ┌─────────────────┐        ┌─────────────────┐
                    │  Milvus Service │        │ NebulaGraph     │        │ Embedding       │
                    │                 │        │ Service         │        │ Service         │
                    │ 向量检索         │        │ 图数据库查询     │        │ 文本向量化       │
                    └─────────────────┘        └─────────────────┘        └─────────────────┘
```

## 🔍 API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
- MCP服务: http://localhost:8000/mcp

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。