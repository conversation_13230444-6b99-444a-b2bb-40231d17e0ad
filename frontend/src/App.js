import React, { useState, useEffect } from 'react';
import { ChakraProvider, extendTheme } from '@chakra-ui/react';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';

// 自定义主题
const theme = extendTheme({
  fonts: {
    heading: `'Roboto', sans-serif`,
    body: `'Roboto', sans-serif`,
  },
  colors: {
    brand: {
      50: '#e5f6ff',
      100: '#b3e0ff',
      200: '#80ccff',
      300: '#4db8ff',
      400: '#1aa3ff',
      500: '#0096ff', // 主色调
      600: '#0077cc',
      700: '#005999',
      800: '#003a66',
      900: '#001c33',
    },
  },
});

// LocalStorage中保存连接状态的key
const CONNECTION_STATUS_KEY = 'nebula_connection_status';

function App() {
  // 从localStorage获取初始连接状态
  const [isLoggedIn, setIsLoggedIn] = useState(() => {
    try {
      const savedConnectionStatus = localStorage.getItem(CONNECTION_STATUS_KEY);
      return savedConnectionStatus === 'true';
    } catch (error) {
      console.error('读取连接状态失败:', error);
      return false;
    }
  });

  // 当连接状态改变时，更新localStorage
  useEffect(() => {
    try {
      localStorage.setItem(CONNECTION_STATUS_KEY, isLoggedIn.toString());
    } catch (error) {
      console.error('保存连接状态失败:', error);
    }
  }, [isLoggedIn]);

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  return (
    <ChakraProvider theme={theme}>
      {isLoggedIn ? (
        <Dashboard onLogout={handleLogout} />
      ) : (
        <Login onLogin={handleLogin} />
      )}
    </ChakraProvider>
  );
}

export default App; 