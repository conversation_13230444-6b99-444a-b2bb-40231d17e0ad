{"name": "nebula-console-frontend", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.0.19", "@chakra-ui/react": "^2.5.5", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "d3": "^7.8.4", "framer-motion": "^10.12.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}