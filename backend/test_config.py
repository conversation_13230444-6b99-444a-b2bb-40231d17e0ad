#!/usr/bin/env python3
"""
测试配置加载的脚本
"""

def test_app_config():
    """测试应用配置"""
    print("🔍 测试应用配置...")
    
    try:
        from app.base.app_config import settings
        
        print("✅ 应用配置加载成功")
        print(f"   项目名称: {settings.PROJECT_NAME}")
        print(f"   API前缀: {settings.API_V1_STR}")
        print(f"   主机: {settings.HOST}")
        print(f"   端口: {settings.PORT}")
        print(f"   环境: {settings.ENV}")
        print(f"   日志级别: {settings.LOG_LEVEL}")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_other_configs():
    """测试其他配置"""
    print("\n🔍 测试其他配置...")
    
    try:
        from app.utils.config_manager import config
        
        print("✅ 配置管理器加载成功")
        print(f"   Milvus URI: {config.milvus_config.get('uri', 'Not configured')}")
        print(f"   Nebula IP: {config.nebula_config.get('ip', 'Not configured')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器加载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试配置加载")
    print("=" * 40)
    
    success_count = 0
    total_tests = 2
    
    if test_app_config():
        success_count += 1
    
    if test_other_configs():
        success_count += 1
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有配置测试通过！")
    else:
        print("⚠️  部分配置测试失败")

if __name__ == "__main__":
    main()
