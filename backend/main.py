#!/usr/bin/env python
"""
知识图谱查询服务入口文件
提供多种运行模式:
1. HTTP模式: python main.py http
   - REST API接口: http://localhost:8000/api/v1/...
   - MCP协议接口: http://localhost:8000/mcp
2. STDIO模式: python main.py stdio
   - 仅提供MCP标准输入输出接口，适合进程间通信
"""
import uvicorn
import os
import sys
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入通用模块
from app.base.logger import setup_logger
from app.utils.config_manager import config
# 使用知识图谱完整MCP实现
from app.api.mcp_endpoints import mcp_server, mount_to_app, get_http_app

# 设置根日志记录器
logger = setup_logger("main")

def create_app():
    """创建集成的应用，同时提供REST API和MCP服务"""
    try:
        # 尝试导入API相关模块
        from app.base.app_config import settings
        from app.api.http_endpoints import router
        has_api_routes = True
    except ImportError:
        logger.warning("未找到API路由模块，仅启动MCP服务")
        has_api_routes = False
    
    logger.info("创建集成应用(REST API + MCP服务)")

    # 先创建FastMCP的HTTP应用，以便获取lifespan上下文管理器
    mcp_http_app = get_http_app()
    
    # 创建主应用，使用MCP应用的lifespan
    app = FastAPI(
        title="知识图谱查询服务",
        description="集成REST API和MCP服务的知识图谱查询系统",
        lifespan=mcp_http_app.router.lifespan_context
    )

    # 设置CORS - 允许所有源访问
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],      # 允许所有源访问，生产环境应该限制为具体域名
        allow_credentials=True,
        allow_methods=["*"],      # 允许所有HTTP方法
        allow_headers=["*"],      # 允许所有HTTP头
        expose_headers=["*"],     # 公开所有响应头
    )

    # 健康检查接口
    @app.get("/")
    def health_check():
        logger.debug("健康检查接口被调用")
        return {
            "status": "healthy", 
            "message": "知识图谱查询服务运行中", 
            "services": ["MCP"] + (["REST API"] if has_api_routes else [])
        }
    
    # 添加API路由（如果可用）
    if has_api_routes:
        logger.info("挂载REST API路由")
        app.include_router(router, prefix=settings.API_V1_STR)

    # 挂载MCP服务到/mcp路径
    logger.info("挂载MCP服务到/mcp路径")
    app = mount_to_app(app, prefix="/mcp")

    return app

# 全局app实例
app = create_app()

# STDIO模式函数
def run_stdio():
    """运行STDIO模式"""
    from fastmcp.cli import run_stdio as fastmcp_run_stdio
    logger.info("以STDIO模式启动MCP服务")
    fastmcp_run_stdio(mcp_server)

if __name__ == "__main__":
    # 获取环境变量配置
    host = os.environ.get("HOST", "0.0.0.0")
    port = int(os.environ.get("PORT", 8000))
    
    # 解析命令行参数，确定运行模式
    mode = "http"  # 默认使用HTTP模式
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    
    if mode == "stdio":
        # STDIO模式 - 用于直接进程通信，只启动MCP服务
        run_stdio()
    else:
        # HTTP模式 - 启动FastAPI服务器
        logger.info(f"以HTTP模式启动服务: host={host}, port={port}")
        uvicorn.run(
            "main:app",  # 使用导入字符串代替直接传递app对象，以支持热重载
            host=host, 
            port=port, 
            reload=False  # 禁用热重载，避免lifespan问题
        )