#!/usr/bin/env python3
"""
开发环境设置脚本
用于安装项目为开发模式，统一导入路径
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"错误: {e.stderr.strip()}")
        return False

def main():
    """主函数"""
    print("🚀 开始设置Maling Query后端开发环境")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)

    print(f"✅ Python版本: {sys.version}")

    # 检查是否在backend目录
    if not Path("pyproject.toml").exists():
        print("❌ 请在backend目录运行此脚本")
        sys.exit(1)
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装项目为开发模式
    if not run_command(f"{sys.executable} -m pip install -e .", "安装项目为开发模式"):
        print("❌ 项目安装失败")
        sys.exit(1)
    
    # 安装开发依赖
    if not run_command(f"{sys.executable} -m pip install -e .[dev]", "安装开发依赖"):
        print("⚠️  开发依赖安装失败，继续...")
    
    # 检查配置文件
    config_file = Path("app/config.properties")
    config_template = Path("app/config.properties.template")

    if not config_file.exists() and config_template.exists():
        print("\n📝 创建配置文件...")
        try:
            import shutil
            shutil.copy(config_template, config_file)
            print("✅ 已创建配置文件，请编辑 app/config.properties")
        except Exception as e:
            print(f"⚠️  配置文件创建失败: {e}")

    print("\n🎉 后端开发环境设置完成!")
    print("\n📋 后续步骤:")
    print("1. 编辑 app/config.properties 配置文件")
    print("2. 启动服务: python main.py")
    print("3. 运行测试: python test_hybrid_search.py")

    print("\n💡 PyCharm 用户提示:")
    print("如果遇到导入错误，请在 PyCharm 中:")
    print("1. 右键点击 backend 目录")
    print("2. 选择 'Mark Directory as' → 'Sources Root'")
    print("3. 或者在 Settings → Project Structure 中将 backend 标记为源代码目录")
    
    # 验证安装
    print("\n🔍 验证安装...")
    try:
        # 测试导入
        from app.base.logger import setup_logger
        from app.utils.config_manager import config
        print("✅ 导入测试通过")
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        print("请检查安装是否正确")

if __name__ == "__main__":
    main()
