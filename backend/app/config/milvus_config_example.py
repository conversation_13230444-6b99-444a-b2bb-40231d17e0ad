"""
Milvus配置示例文件
演示如何使用新的配置方式来初始化MilvusService
"""

import sys
from pathlib import Path

# 添加backend目录到系统路径
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

from app.service.milvus_service import (
    MilvusService,
    MilvusConnectionConfig,
    MilvusFieldConfig
)


def create_custom_milvus_service():
    """创建自定义配置的Milvus服务示例"""

    # 连接配置
    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        token="your_token_here",
        collection_name="custom_collection",
        dimension=768
    )

    # 字段配置 - 简单直接
    field_config = MilvusFieldConfig(
        id_field="custom_id",
        dense_vector_field="custom_dense_vector",
        sparse_vector_field="custom_sparse_vector",
        output_fields=["custom_id", "custom_content", "custom_node_type", "custom_full_name"]
    )

    # 创建服务实例
    milvus_service = MilvusService(
        connection_config=connection_config,
        field_config=field_config
    )

    return milvus_service


def create_milvus_service_from_dict():
    """从字典配置创建Milvus服务示例"""

    # 从字典或其他配置源创建配置
    config_dict = {
        "uri": "http://localhost:19530",
        "token": "your_token_here",
        "collection_name": "my_collection",
        "dimension": 1024,
        "fields": {
            "id": "doc_id",
            "dense_vector": "embedding",
            "output_fields": ["doc_id", "title", "content", "category"]
        }
    }

    connection_config = MilvusConnectionConfig(
        uri=config_dict["uri"],
        token=config_dict.get("token"),
        collection_name=config_dict["collection_name"],
        dimension=config_dict.get("dimension")
    )

    field_config = MilvusFieldConfig(
        id_field=config_dict["fields"]["id"],
        dense_vector_field=config_dict["fields"]["dense_vector"],
        output_fields=config_dict["fields"]["output_fields"]
    )

    milvus_service = MilvusService(
        connection_config=connection_config,
        field_config=field_config
    )

    return milvus_service


def create_milvus_service_with_env_vars():
    """从环境变量创建Milvus服务示例"""
    import os

    connection_config = MilvusConnectionConfig(
        uri=os.getenv("MILVUS_URI", "http://localhost:19530"),
        token=os.getenv("MILVUS_TOKEN"),
        collection_name=os.getenv("MILVUS_COLLECTION", "default_collection"),
        dimension=int(os.getenv("MILVUS_DIMENSION", "768"))
    )

    field_config = MilvusFieldConfig(
        id_field=os.getenv("MILVUS_ID_FIELD", "id"),
        dense_vector_field=os.getenv("MILVUS_VECTOR_FIELD", "embedding"),
        output_fields=os.getenv("MILVUS_OUTPUT_FIELDS", "id,content").split(",")
    )

    milvus_service = MilvusService(
        connection_config=connection_config,
        field_config=field_config
    )

    return milvus_service


def create_document_collection_service():
    """为文档集合创建专门的Milvus服务"""

    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        collection_name="document_vectors",
        dimension=768
    )

    # 文档集合的字段配置
    field_config = MilvusFieldConfig(
        id_field="doc_id",
        dense_vector_field="embedding",
        output_fields=["doc_id", "document_text", "doc_title", "doc_author", "doc_category", "created_at"]
    )

    return MilvusService(connection_config=connection_config, field_config=field_config)


def create_code_collection_service():
    """为代码集合创建专门的Milvus服务"""

    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        collection_name="code_vectors",
        dimension=1024
    )

    # 代码集合的字段配置
    field_config = MilvusFieldConfig(
        id_field="code_id",
        dense_vector_field="code_embedding",
        sparse_vector_field="keyword_embedding",
        output_fields=["code_id", "source_code", "func_name", "class_name", "file_path", "prog_language", "repository_id"]
    )

    return MilvusService(connection_config=connection_config, field_config=field_config)


def usage_examples():
    """使用示例"""

    print("=== Milvus服务配置示例 ===")

    # 示例1: 自定义配置
    print("\n1. 使用自定义配置:")
    custom_service = create_custom_milvus_service()
    print(f"自定义服务创建成功: {type(custom_service).__name__}")

    # 示例2: 从字典配置创建
    print("\n2. 从字典配置创建:")
    dict_service = create_milvus_service_from_dict()
    print(f"字典配置服务创建成功: {type(dict_service).__name__}")

    # 示例3: 从环境变量创建
    print("\n3. 从环境变量创建:")
    env_service = create_milvus_service_with_env_vars()
    print(f"环境变量服务创建成功: {type(env_service).__name__}")

    # 示例4: 文档集合专用服务
    print("\n4. 文档集合专用服务:")
    doc_service = create_document_collection_service()
    print(f"文档服务创建成功: {type(doc_service).__name__}")

    # 示例5: 代码集合专用服务
    print("\n5. 代码集合专用服务:")
    code_service = create_code_collection_service()
    print(f"代码服务创建成功: {type(code_service).__name__}")

    # 演示字段配置
    print("\n=== 字段配置示例 ===")
    field_config = MilvusFieldConfig(
        id_field="my_id",
        dense_vector_field="my_vector",
        sparse_vector_field="my_sparse_vector",
        output_fields=["my_id", "content", "title", "category"]
    )
    print(f"字段配置: ID={field_config.id_field}, 向量={field_config.dense_vector_field}")
    print(f"输出字段: {field_config.output_fields}")


if __name__ == "__main__":
    usage_examples()
