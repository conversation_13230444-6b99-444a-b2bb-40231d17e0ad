# Milvus 测试工具

此目录包含用于Milvus操作的测试工具，这些工具使用项目的`ConfigManager`来获取Milvus配置。

## 版本兼容性说明

当前脚本需要特定版本的依赖项才能正常工作。如果遇到以下错误：

```
_SUPPORTS_LOAD_DEFAULT = ma.__version_info__ >= (3, 13)
AttributeError: module 'marshmallow' has no attribute '__version_info__'
```

这是因为`pymilvus`依赖的`environs`包与新版`marshmallow`不兼容。请通过以下命令安装指定版本：

```bash
pip install pymilvus==2.3.0 marshmallow<4.0.0,>=3.0.0 environs<9.0.0
```

或直接安装项目依赖：

```bash
pip install -r backend/requirements.txt
```

## 准备工作

确保已经正确配置了`backend/app/config.properties`文件，或设置了相应的环境变量。

例如，最基本的配置需要提供Milvus的连接URI：

```
[MILVUS]
uri = http://localhost:19530
```

## 脚本说明

### 1. 查询Milvus集合信息 (milvus_query_info.py)

此脚本用于查询Milvus中所有集合的schema和节点总数。

**功能**:
- 显示所有集合的列表
- 显示每个集合的实体数量
- 显示每个集合的字段信息和类型
- 显示索引信息

**使用方法**:
```bash
python backend/app/test/milvus_query_info.py
```

### 2. 删除Milvus集合 (milvus_drop_collections.py)

此脚本用于删除Milvus中的集合，支持删除指定集合或所有集合。

**功能**:
- 交互式选择删除操作
- 支持命令行参数指定操作
- 删除前确认，防止误操作

**使用方法**:

交互式使用:
```bash
python backend/app/test/milvus_drop_collections.py
```

命令行参数:
```bash
# 删除指定集合
python backend/app/test/milvus_drop_collections.py -c collection_name

# 删除所有集合
python backend/app/test/milvus_drop_collections.py -a

# 强制删除（不需确认）
python backend/app/test/milvus_drop_collections.py -a -f

# 自动对所有提示回答"是"
python backend/app/test/milvus_drop_collections.py -y
```

**参数说明**:
- `-a, --all`: 删除所有集合
- `-c, --collection`: 指定要删除的集合名称
- `-f, --force`: 强制删除，不需要确认
- `-y, --yes`: 对所有提示自动回答"是"

## 常见问题解决

1. **ImportError: 导入pymilvus失败**:
   确保已安装正确版本的pymilvus及其依赖

2. **连接Milvus服务器失败**:
   检查Milvus服务是否启动，URI和端口是否正确

3. **配置文件不存在**:
   请复制`backend/app/config.properties.template`为`backend/app/config.properties`并填写相应配置

4. **路径导入错误**:
   确保从项目根目录运行脚本，或者正确设置了PYTHONPATH

## 注意事项

1. 删除操作不可恢复，请谨慎使用
2. 配置文件优先级：环境变量 > 配置文件
3. 确保Milvus服务器已启动并可访问 