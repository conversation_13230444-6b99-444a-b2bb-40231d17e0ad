import os
from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import List, Optional


class Settings(BaseSettings):
    """应用配置"""
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Maling Query 知识图谱查询服务"

    # CORS设置
    BACKEND_CORS_ORIGINS: List[str] = ["*"]

    # 服务器设置
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 环境变量（可选）
    PYTHONPATH: Optional[str] = None
    ENV: str = "development"
    LOG_LEVEL: str = "INFO"

    # 使用 Pydantic V2 的 ConfigDict
    model_config = ConfigDict(
        case_sensitive=True,
        env_file=".env",
        env_file_encoding="utf-8",
        extra="ignore"  # 忽略额外的环境变量
    )


settings = Settings()