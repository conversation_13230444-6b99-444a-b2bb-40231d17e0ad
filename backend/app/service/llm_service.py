import logging
from openai import OpenAI
from app.base.logger import setup_logger
from app.utils.config_manager import config
from typing import List, Dict, Any, Optional

# 使用新的日志配置
logger = setup_logger("llm_service")

class LLMService:
    """
    大型语言模型服务，用于生成摘要和处理文本
    """
    def __init__(self):
        self.model = None
        self.api_key = None
        self.api_base = None
        self.client = None
        self.initialized = False
        self.error_message = None

    def initialize(self, model=None, api_key=None, api_base=None):
        """
        初始化LLM服务
        
        Args:
            model: 模型名称
            api_key: API密钥
            api_base: API基础URL
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 使用提供的参数或配置中的默认值
            self.model = model or config.llm_config.get("model")
            self.api_key = api_key or config.llm_config.get("api_key")
            self.api_base = api_base or config.llm_config.get("api_base")
            
            logger.info(f"初始化LLM服务: 模型={self.model}, API基础URL={self.api_base}")
            
            # 创建OpenAI客户端实例，与嵌入服务保持一致
            self.client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base  # 确保这里包含 "/v1" 或在配置中已包含
            )
            
            self.initialized = True
            self.error_message = None
            logger.info("LLM服务初始化成功")
            return True
            
        except Exception as e:
            logger.exception(f"初始化LLM服务失败: {str(e)}")
            self.error_message = f"初始化异常: {str(e)}"
            self.initialized = False
            return False
    
    def generate_summary(self, content: str, max_tokens: int = 100) -> str:
        """
        为给定内容生成摘要
        
        Args:
            content: 输入内容
            max_tokens: 生成的最大token数
        
        Returns:
            生成的摘要文本
        """
        if not self.initialized:
            logger.error("LLM服务未初始化，无法执行生成")
            return "LLM服务未初始化"
        
        try:
            if not content or content.strip() == "":
                logger.warning("生成摘要的内容为空")
                return ""
            
            logger.debug(f"为内容生成摘要: {content[:50]}...")
            
            # 使用客户端实例调用OpenAI API生成摘要
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个精确的代码摘要生成器。请为以下代码生成简短、准确的摘要，总结其功能和用途。摘要应该简洁，不超过50个字。"},
                    {"role": "user", "content": content}
                ],
                max_tokens=max_tokens,
                temperature=0.3
            )
            
            # 提取生成的摘要
            summary = response.choices[0].message.content.strip()
            
            logger.debug(f"摘要生成完成: {summary}")
            return summary
            
        except Exception as e:
            logger.exception(f"摘要生成失败: {str(e)}")
            self.error_message = f"生成异常: {str(e)}"
            return f"摘要生成失败: {str(e)[:100]}"
    
    def batch_generate_summaries(self, contents: List[str], max_tokens: int = 100) -> List[str]:
        """
        批量为多个内容生成摘要
        
        Args:
            contents: 输入内容列表
            max_tokens: 每个摘要生成的最大token数
        
        Returns:
            生成的摘要文本列表
        """
        if not self.initialized:
            logger.error("LLM服务未初始化，无法执行生成")
            return ["LLM服务未初始化"] * len(contents)
        
        summaries = []
        for content in contents:
            summary = self.generate_summary(content, max_tokens)
            summaries.append(summary)
        
        return summaries
    
    def get_error_message(self):
        """获取最后一次错误信息"""
        return getattr(self, 'error_message', "未知错误")

# 创建全局实例
llm_service = LLMService() 