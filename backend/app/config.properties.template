# ===========================================================================
# 马令项目配置文件模板
# 使用方法:
# 1. 复制此文件为config.properties
# 2. 根据实际环境填写各项配置
# 3. 也可以通过环境变量覆盖配置项，格式为：SECTION_OPTION
# ===========================================================================

# NebulaGraph配置
[NEBULA]
# NebulaGraph服务器IP地址
ip = 

# NebulaGraph服务端口
port = 

# 用户名
user = 

# 密码
password = 

# 图空间名称
space = 

# Milvus配置
[MILVUS]
# Milvus服务器连接URI (HTTP)
uri = 

# Milvus认证令牌，格式为：username:password
token = 

# Milvus Lite本地连接数据库路径
db_path = 

# 向量集合名称
collection_name = 

# 向量维度
dimension = 

# 嵌入模型配置
[EMBEDDING]
# 嵌入模型名称
model_name = 

# API密钥
api_key = 

# API基础URL
api_base = 

# LLM配置
[LLM]
# 模型名称
model = 

# API密钥
api_key = 

# API基础URL
api_base = 