from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional


class ConnectionRequest(BaseModel):
    """数据库连接请求模型"""
    ip: str = Field(..., description="NebulaGraph数据库IP")
    port: int = Field(..., description="NebulaGraph数据库端口")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    space_name: str = Field(..., description="图空间名")


class MethodSearchRequest(BaseModel):
    """方法搜索请求模型"""
    method_name: str = Field(..., description="方法名(部分或完整)")


class NodeRequest(BaseModel):
    """节点请求模型"""
    method_full_name: str = Field(..., description="方法完整名称")
    query_type: str = Field(..., description="查询类型: self, upstream, downstream")
    path_depth: int = Field(1, description="路径深度，默认为1，-1表示全部")


class NodeResponse(BaseModel):
    """节点响应模型"""
    nodes: List[Dict[str, Any]] = Field([], description="节点列表")
    edges: List[Dict[str, Any]] = Field([], description="边列表")


class NodeDetailRequest(BaseModel):
    """节点详情请求模型"""
    node_id: str = Field(..., description="节点ID")
    

class NodeDetail(BaseModel):
    """节点详情响应模型"""
    id: str = Field(..., description="节点ID")
    name: str = Field(..., description="方法名")
    full_name: str = Field(..., description="方法完整名称")
    content: str = Field(None, description="解压后的内容")
    raw_properties: Dict[str, Any] = Field({}, description="原始属性") 