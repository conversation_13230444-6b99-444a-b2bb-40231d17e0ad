import base64
import zlib
import logging
import os
import sys

from app.base.logger import setup_logger

# 使用新的日志配置
logger = setup_logger("content_processor")

class ContentProcessor:
    """
    用于处理NebulaGraph中存储的内容，如解压缩等
    """
    
    @staticmethod
    def decompress(encoded_content: str) -> str:
        """
        解压缩Base64编码的内容
        
        Args:
            encoded_content: 编码并压缩的内容
            
        Returns:
            原始文本内容
        """
        try:
            if not encoded_content:
                logger.warning("尝试解压空内容")
                return ""
            
            # Base64解码
            logger.debug("开始Base64解码")
            compressed = base64.b64decode(encoded_content)
            
            # zlib解压缩
            logger.debug("开始zlib解压缩")
            decompressed = zlib.decompress(compressed)
            
            # 转换为字符串
            content = decompressed.decode('utf-8')
            logger.debug(f"解压完成，内容长度: {len(content)}")
            
            return content
        except Exception as e:
            logger.exception(f"解压内容失败: {str(e)}")
            # 如果解压失败，返回空字符串
            return "" 
        

            
if __name__ == "__main__":
    # 测试解压缩功能
    result = ContentProcessor.decompress("eNpzCMpNCskvyEzWKAGRCrYKjsGRfs7xgaGuQZHxbp7O/joKJUWJecXO+SmpQFmN4PzSouRUv8TcVD2QrIK2glK8kiaQCkotyC8qASkDS8Q7+/sFh/q6Bjv7B7kCzQCZ7gsxIwTG1gPbpaNQUJRfkp+cnwOUC4Ay9Zz8Qzw0AZu/Mng=")
    print("解压结果:", result)