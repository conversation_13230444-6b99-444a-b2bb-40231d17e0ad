#!/usr/bin/env python3
"""
测试混合检索功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加backend目录到Python路径（仅用于测试脚本）
current_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(current_dir))

from app.service.knowledge_graph_service import knowledge_graph_service

def test_hybrid_search():
    """测试混合检索功能"""
    print("=== 测试混合检索功能 ===")
    
    # 初始化知识图谱服务
    print("1. 初始化知识图谱服务...")
    success = knowledge_graph_service.initialize()
    if not success:
        print(f"初始化失败: {knowledge_graph_service.error_message}")
        return False
    
    print("初始化成功!")
    
    # 测试搜索
    print("\n2. 执行混合检索测试...")
    query_text = "python函数定义"
    repo_id = "test_repo"  # 请根据实际情况修改
    
    try:
        results = knowledge_graph_service.search_knowledge_graph(
            query_text=query_text,
            repo_id=repo_id,
            top_k=3
        )
        
        if "error" in results:
            print(f"搜索失败: {results['error']}")
            return False
        
        print(f"搜索成功! 查询: {results.get('query_text', '')}")
        print(f"结果数量: {len(results.get('results', []))}")
        
        # 显示前几个结果
        for i, result in enumerate(results.get('results', [])[:3]):
            print(f"\n结果 {i+1}:")
            print(f"  ID: {result.get('id', '')}")
            print(f"  类型: {result.get('node_type', '')}")
            print(f"  名称: {result.get('full_name', '')}")
            print(f"  摘要: {result.get('summary', '')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"搜索异常: {str(e)}")
        return False
    
    finally:
        # 关闭连接
        knowledge_graph_service.close()

def test_milvus_fields():
    """测试Milvus字段常量"""
    print("\n=== 测试Milvus字段常量 ===")
    
    from app.service.milvus_service import MilvusFields
    
    print("字段常量定义:")
    print(f"  ID_FIELD: {MilvusFields.ID_FIELD}")
    print(f"  TEXT_DENSE: {MilvusFields.TEXT_DENSE}")
    print(f"  TEXT_SPARSE: {MilvusFields.TEXT_SPARSE}")
    print(f"  NODE_TYPE_FIELD: {MilvusFields.NODE_TYPE_FIELD}")
    print(f"  FULL_NAME_FIELD: {MilvusFields.FULL_NAME_FIELD}")
    print(f"  CONTENT: {MilvusFields.CONTENT}")
    print(f"  REPO_ID_FIELD: {MilvusFields.REPO_ID_FIELD}")
    print(f"  BRANCH_NAME_FIELD: {MilvusFields.BRANCH_NAME_FIELD}")

if __name__ == "__main__":
    print("开始测试混合检索功能...")
    
    # 测试字段常量
    test_milvus_fields()
    
    # 测试混合检索
    success = test_hybrid_search()
    
    if success:
        print("\n✅ 测试完成，混合检索功能正常!")
    else:
        print("\n❌ 测试失败，请检查配置和连接!")
