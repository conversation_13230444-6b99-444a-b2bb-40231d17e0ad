#!/usr/bin/env python3
"""
项目运行脚本
统一的项目启动入口
"""

import sys
import os
import subprocess
from pathlib import Path

def run_main(args=None):
    """运行主程序"""
    # 构建命令
    cmd = [sys.executable, "main.py"]
    if args:
        cmd.extend(args)

    # 运行程序
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)

def run_test():
    """运行测试"""
    test_file = Path("test_hybrid_search.py")

    if not test_file.exists():
        print("测试文件不存在")
        sys.exit(1)

    try:
        subprocess.run([sys.executable, "test_hybrid_search.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"测试运行失败: {e}")
        sys.exit(1)

def show_help():
    """显示帮助信息"""
    print("""
Maling Query 后端运行脚本

用法:
    python run.py [选项]

选项:
    http        启动HTTP服务器模式 (默认)
    stdio       启动STDIO模式
    test        运行测试
    help        显示此帮助信息

示例:
    python run.py              # 启动HTTP服务器
    python run.py http         # 启动HTTP服务器
    python run.py stdio        # 启动STDIO模式
    python run.py test         # 运行测试
    """)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        # 默认启动HTTP模式
        run_main(["http"])
        return
    
    command = sys.argv[1].lower()
    
    if command in ["help", "-h", "--help"]:
        show_help()
    elif command == "test":
        run_test()
    elif command in ["http", "stdio"]:
        run_main([command])
    else:
        print(f"未知命令: {command}")
        show_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
