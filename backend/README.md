# 知识图谱查询MCP服务

基于FastMCP的知识图谱查询服务，通过Milvus向量数据库和NebulaGraph图数据库提供代码知识图谱查询功能。

## 系统架构

系统由以下核心组件组成：

1. **FastMCP服务器**：提供MCP工具接口，支持对话会话管理
2. **知识图谱服务**：整合向量搜索和图数据库查询
3. **Milvus服务**：提供向量相似度搜索功能
4. **NebulaGraph服务**：提供图数据存储和查询功能
5. **嵌入服务**：将文本转换为向量表示

## 提供的MCP工具

服务提供以下MCP工具：

1. **start_session**：开启对话会话，返回sessionId
2. **query_knowledge_graph**：查询知识图谱，返回节点摘要
3. **get_node_content**：根据节点ID获取节点详细内容
4. **close_session**：关闭会话，清理会话数据

## 配置说明

配置文件支持以下主要配置项：

- **NebulaGraph配置**：连接信息和图空间名称
- **Milvus配置**：连接信息和集合配置
- **嵌入模型配置**：使用的嵌入模型和API设置

## 如何运行

### 方式一：使用统一脚本（推荐）

1. 进入backend目录：
```bash
cd backend
```

2. 设置开发环境：
```bash
python setup_dev.py
```

3. 配置环境：
- 编辑配置文件 `app/config.properties`
- 确保Milvus和NebulaGraph服务已启动

4. 启动服务：
```bash
# HTTP模式（默认）
python run.py

# STDIO模式
python run.py stdio

# 运行测试
python run.py test
```

### 方式二：直接运行

1. 进入backend目录：
```bash
cd backend
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 启动服务：
```bash
python main.py
```

服务将同时启动REST API和MCP服务，默认运行在：
- MCP服务：http://127.0.0.1:8000/mcp
- REST API：http://127.0.0.1:8000/api/v1/...

可以通过环境变量 `HOST` 和 `PORT` 自定义监听地址和端口：
```bash
HOST=0.0.0.0 PORT=9000 python main.py
```

## 调用流程

1. LLM调用 `start_session` 工具开启对话，获取sessionId
2. LLM调用 `query_knowledge_graph` 查询知识图谱，获取节点摘要
3. LLM针对感兴趣的节点调用 `get_node_content` 获取详细内容
4. LLM可以调用 `close_session` 关闭会话清理资源 